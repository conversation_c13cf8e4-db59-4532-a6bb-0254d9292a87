// orders_bloc.dart
import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import '../../../../customer/models/customer_model.dart';
import '../../../../location/models/district_model.dart';
import '../../../../tree/models/tree_variety_model.dart';
import '../../../datasources/order_remote_datasources.dart';
import '../../../models/order_model.dart';

part 'orders_event.dart';
part 'orders_state.dart';

class OrdersBloc extends Bloc<OrdersEvent, OrdersState> {
  final OrderRemoteDatasourceImpl orderRemoteDatasource;

  OrdersBloc({required this.orderRemoteDatasource}) : super(OrdersState()) {
    on<LoadOrders>(_onLoadOrders);
    on<RefreshOrders>(_onRefreshOrders);
    on<LoadMoreOrders>(_onLoadMoreOrders);
    on<FilterOrdersByStatus>(_onFilterOrdersByStatus);
    on<FilterOrdersByDate>(_onFilterOrdersByDate);
    on<CreateOrder>(_onCreateOrder);

    // New events for location and data loading
    on<LoadProvinces>(_onLoadProvinces);
    on<LoadRegions>(_onLoadRegions);
    on<LoadDistricts>(_onLoadDistricts);
    on<LoadTrees>(_onLoadTrees);
    on<LoadClients>(_onLoadClients);
    on<LoadVarieties>(_onLoadVarieties);

    // Address selection events
    on<SelectProvince>(_onSelectProvince);
    on<SelectRegion>(_onSelectRegion);
    on<SelectDistrict>(_onSelectDistrict);
    on<ClearAddressSelection>(_onClearAddressSelection);
  }

  Future<void> _onLoadOrders(LoadOrders event, Emitter<OrdersState> emit) async {
    try {
      emit(state.copyWith(isLoading: true, error: null));

      final response = await orderRemoteDatasource.getAllOrders(
        page: event.page,
        limit: event.limit,
        status: event.status,
        date: event.date,
      );

      emit(state.copyWith(
        isLoading: false,
        orders: response.orders,
        hasMore: response.hasNextPage,
        currentPage: response.page,
        totalPages: response.totalPages,
        currentStatus: event.status,
        currentDate: event.date,
        error: null,
      ));
    } on DioException catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.message ?? 'Xatolik yuz berdi',
      ));
    } catch (e) {
      print('Exception in getAllOrders: $e');
      print('Exception type: ${e.runtimeType}');
      if (e is TypeError) {
        print('TypeError details: $e');
      }
      emit(state.copyWith(
        isLoading: false,
        error: 'Kutilmagan xatolik yuz berdi: $e',
      ));
    }
  }

  Future<void> _onRefreshOrders(RefreshOrders event, Emitter<OrdersState> emit) async {
    try {
      emit(state.copyWith(error: null));

      final response = await orderRemoteDatasource.getAllOrders(
        page: 1,
        limit: 10,
        status: state.currentStatus,
        date: state.currentDate,
      );

      emit(state.copyWith(
        orders: response.orders,
        hasMore: response.hasNextPage,
        currentPage: response.page,
        totalPages: response.totalPages,
        error: null,
      ));
    } on DioException catch (e) {
      emit(state.copyWith(
        error: e.message ?? 'Xatolik yuz berdi',
      ));
    } catch (e) {
      print('Exception in refreshOrders: $e');
      print('Exception type: ${e.runtimeType}');
      if (e is TypeError) {
        print('TypeError details: $e');
      }
      emit(state.copyWith(
        error: 'Kutilmagan xatolik yuz berdi: $e',
      ));
    }
  }

  Future<void> _onLoadMoreOrders(LoadMoreOrders event, Emitter<OrdersState> emit) async {
    if (!state.hasMore || state.isLoadingMore) return;

    try {
      emit(state.copyWith(isLoadingMore: true, error: null));

      final nextPage = state.currentPage + 1;
      final response = await orderRemoteDatasource.getAllOrders(
        page: nextPage,
        limit: 10,
        status: state.currentStatus,
        date: state.currentDate,
      );

      final updatedOrders = [...state.orders, ...response.orders];

      emit(state.copyWith(
        isLoadingMore: false,
        orders: updatedOrders,
        hasMore: response.hasNextPage,
        currentPage: response.page,
        totalPages: response.totalPages,
        error: null,
      ));
    } on DioException catch (e) {
      emit(state.copyWith(
        isLoadingMore: false,
        error: e.message ?? 'Xatolik yuz berdi',
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoadingMore: false,
        error: 'Kutilmagan xatolik yuz berdi',
      ));
    }
  }

  Future<void> _onFilterOrdersByStatus(FilterOrdersByStatus event, Emitter<OrdersState> emit) async {
    try {
      emit(state.copyWith(isLoading: true, error: null));

      final response = await orderRemoteDatasource.getAllOrders(
        page: 1,
        limit: 10,
        status: event.status,
        date: state.currentDate,
      );

      emit(state.copyWith(
        isLoading: false,
        orders: response.orders,
        hasMore: response.hasNextPage,
        currentPage: response.page,
        totalPages: response.totalPages,
        currentStatus: event.status,
        error: null,
      ));
    } on DioException catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.message ?? 'Xatolik yuz berdi',
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: 'Kutilmagan xatolik yuz berdi',
      ));
    }
  }

  Future<void> _onFilterOrdersByDate(FilterOrdersByDate event, Emitter<OrdersState> emit) async {
    try {
      emit(state.copyWith(isLoading: true, error: null));

      final response = await orderRemoteDatasource.getAllOrders(
        page: 1,
        limit: 10,
        status: state.currentStatus,
        date: event.date,
      );

      emit(state.copyWith(
        isLoading: false,
        orders: response.orders,
        hasMore: response.hasNextPage,
        currentPage: response.page,
        totalPages: response.totalPages,
        currentDate: event.date,
        error: null,
      ));
    } on DioException catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.message ?? 'Xatolik yuz berdi',
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: 'Kutilmagan xatolik yuz berdi',
      ));
    }
  }

  Future<void> _onCreateOrder(CreateOrder event, Emitter<OrdersState> emit) async {
    try {
      emit(state.copyWith(isCreating: true, error: null, successMessage: null));

      final result = await orderRemoteDatasource.createOrder(event.request);

      if (result['success'] == true) {
        emit(state.copyWith(
          isCreating: false,
          successMessage: result['message'] ?? 'Buyurtma muvaffaqiyatli yaratildi',
          error: null,
        ));

        // Refresh orders list after successful creation
        add(RefreshOrders());
      } else {
        emit(state.copyWith(
          isCreating: false,
          error: result['message'] ?? 'Buyurtma yaratishda xatolik',
        ));
      }
    } on DioException catch (e) {
      String errorMessage;

      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.sendTimeout ||
          e.type == DioExceptionType.receiveTimeout) {
        errorMessage = 'Vaqt tugadi. Iltimos qaytadan urinib ko\'ring.';
      } else if (e.response?.statusCode == 413) {
        errorMessage = 'Fayl hajmi juda katta. Kichikroq fayl tanlang.';
      } else if (e.type == DioExceptionType.connectionError) {
        errorMessage = 'Internet aloqasi yo\'q';
      } else if (e.response?.statusCode == 400) {
        // Handle 400 errors - extract server message if available
        final responseData = e.response?.data;
        if (responseData is String && responseData.isNotEmpty) {
          errorMessage = responseData; // Use server message directly (e.g., "Unsupported file format")
        } else if (responseData is Map<String, dynamic>) {
          errorMessage = responseData['message'] ?? responseData['error'] ?? 'Buyurtma yaratishda xatolik';
        } else {
          errorMessage = 'Buyurtma yaratishda xatolik';
        }
      } else {
        errorMessage = e.message ?? 'Buyurtma yaratishda xatolik';
      }

      emit(state.copyWith(
        isCreating: false,
        error: errorMessage,
      ));
    } catch (e) {
      emit(state.copyWith(
        isCreating: false,
        error: 'Kutilmagan xatolik yuz berdi',
      ));
    }
  }

  Future<void> _onLoadProvinces(LoadProvinces event, Emitter<OrdersState> emit) async {
    try {
      emit(state.copyWith(isLoadingProvinces: true, error: null));

      final response = await orderRemoteDatasource.getAllProvinces(
        page: 1,
        limit: 50,
      );

      emit(state.copyWith(
        isLoadingProvinces: false,
        provinces: response.provinces,
        error: null,
      ));
    } on DioException catch (e) {
      emit(state.copyWith(
        isLoadingProvinces: false,
        error: e.message ?? 'Viloyatlarni yuklashda xatolik',
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoadingProvinces: false,
        error: 'Kutilmagan xatolik yuz berdi',
      ));
    }
  }

  Future<void> _onLoadRegions(LoadRegions event, Emitter<OrdersState> emit) async {
    try {
      emit(state.copyWith(isLoadingRegions: true, error: null));

      final response = await orderRemoteDatasource.getRegionsByProvince(
        provinceId: event.provinceId,
        page: 1,
        limit: 50,
      );

      emit(state.copyWith(
        isLoadingRegions: false,
        regions: response.regions,
        error: null,
      ));
    } on DioException catch (e) {
      emit(state.copyWith(
        isLoadingRegions: false,
        error: e.message ?? 'Tumanlarni yuklashda xatolik',
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoadingRegions: false,
        error: 'Kutilmagan xatolik yuz berdi',
      ));
    }
  }

  Future<void> _onLoadDistricts(LoadDistricts event, Emitter<OrdersState> emit) async {
    try {
      emit(state.copyWith(isLoadingDistricts: true, error: null));

      final response = await orderRemoteDatasource.getDistrictsByRegion(
        regionId: event.regionId,
        page: 1,
        limit: 50,
      );

      emit(state.copyWith(
        isLoadingDistricts: false,
        districts: response.districts,
        error: null,
      ));
    } on DioException catch (e) {
      emit(state.copyWith(
        isLoadingDistricts: false,
        error: e.message ?? 'Shaharlarni yuklashda xatolik',
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoadingDistricts: false,
        error: 'Kutilmagan xatolik yuz berdi',
      ));
    }
  }

  Future<void> _onLoadTrees(LoadTrees event, Emitter<OrdersState> emit) async {
    try {
      emit(state.copyWith(isLoadingTrees: true, error: null));

      final response = await orderRemoteDatasource.getAllTrees(
        page: 1,
        limit: 100,
        variety: event.variety,
      );

      emit(state.copyWith(
        isLoadingTrees: false,
        trees: response.trees,
        error: null,
      ));
    } on DioException catch (e) {
      emit(state.copyWith(
        isLoadingTrees: false,
        error: e.message ?? 'Daraxtlarni yuklashda xatolik',
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoadingTrees: false,
        error: 'Kutilmagan xatolik yuz berdi',
      ));
    }
  }

  Future<void> _onLoadClients(LoadClients event, Emitter<OrdersState> emit) async {
    try {
      emit(state.copyWith(isLoadingClients: true, error: null));

      final response = await orderRemoteDatasource.getAllClients(
        page: 1,
        limit: 100,
        province: event.province,
        region: event.region,
        search: event.search,
      );

      emit(state.copyWith(
        isLoadingClients: false,
        clients: response.clients,
        error: null,
      ));
    } on DioException catch (e) {
      emit(state.copyWith(
        isLoadingClients: false,
        error: e.message ?? 'Mijozlarni yuklashda xatolik',
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoadingClients: false,
        error: 'Kutilmagan xatolik yuz berdi',
      ));
    }
  }

  Future<void> _onLoadVarieties(LoadVarieties event, Emitter<OrdersState> emit) async {
    try {
      emit(state.copyWith(isLoadingVarieties: true, error: null));

      final response = await orderRemoteDatasource.getAllVarieties(
        page: 1,
        limit: 50,
      );



      emit(state.copyWith(
        isLoadingVarieties: false,
        varieties: response.varieties,
        error: null,
      ));
    } on DioException catch (e) {
      emit(state.copyWith(
        isLoadingVarieties: false,
        error: e.message ?? 'Navlarni yuklashda xatolik',
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoadingVarieties: false,
        error: 'Kutilmagan xatolik yuz berdi',
      ));
    }
  }

  Future<void> _onSelectProvince(SelectProvince event, Emitter<OrdersState> emit) async {
    emit(state.copyWith(
      selectedProvince: event.province,
      selectedRegion: null,
      selectedDistrict: null,
      regions: null,
      districts: null,
    ));

    // Load regions for the selected province
    add(LoadRegions(event.province.id));
  }

  Future<void> _onSelectRegion(SelectRegion event, Emitter<OrdersState> emit) async {
    emit(state.copyWith(
      selectedRegion: event.region,
      selectedDistrict: null,
      districts: null,
    ));

    // Load districts for the selected region
    add(LoadDistricts(event.region.id));
  }

  Future<void> _onSelectDistrict(SelectDistrict event, Emitter<OrdersState> emit) async {
    emit(state.copyWith(selectedDistrict: event.district));
  }

  Future<void> _onClearAddressSelection(ClearAddressSelection event, Emitter<OrdersState> emit) async {
    emit(state.copyWith(
      selectedProvince: null,
      selectedRegion: null,
      selectedDistrict: null,
      regions: null,
      districts: null,
    ));
  }

  // Helper methods
  void clearSuccessMessage() {
    emit(state.copyWith(successMessage: null));
  }

  void clearError() {
    emit(state.copyWith(error: null));
  }
}